import React from 'react';
import { ShoppingC<PERSON>, Eye, Heart } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useCart } from '../contexts/CartContext';
import StampPlaceholder from './StampPlaceholder';

const StampCard = ({ stamp }) => {
  const { t } = useLanguage();
  const { addToCart } = useCart();

  if (!stamp || !stamp.attributes) {
    return null;
  }

  const { id, attributes } = stamp;
  const {
    nom: title,
    prix: price,
    image,
    categorie,
    description
  } = attributes;

  // Handle image URL
  const imageUrl = image?.data?.attributes?.url 
    ? `http://localhost:1337${image.data.attributes.url}`
    : null;

  // Handle category name
  const categoryName = categorie?.data?.attributes?.name || t('stamps.category');

  // Determine category for placeholder
  const getCategoryKey = (catName) => {
    if (!catName) return 'heritage';
    const name = catName.toLowerCase();
    if (name.includes('patrimoine') || name.includes('heritage')) return 'heritage';
    if (name.includes('nature') || name.includes('paysage')) return 'nature';
    if (name.includes('histoire') || name.includes('history')) return 'history';
    if (name.includes('culture') || name.includes('art')) return 'culture';
    if (name.includes('sport')) return 'sports';
    if (name.includes('événement') || name.includes('special')) return 'special-events';
    return 'heritage';
  };

  // Handle description - convert rich text to plain text
  const getPlainTextDescription = (desc) => {
    if (!desc || !Array.isArray(desc)) return '';
    return desc
      .map(block => 
        block.children?.map(child => child.text).join(' ') || ''
      )
      .join(' ')
      .substring(0, 100) + '...';
  };

  const plainDescription = getPlainTextDescription(description);

  const handleAddToCart = () => {
    const cartItem = {
      id: stamp.id,
      title,
      price,
      image: imageUrl,
      category: categoryName,
      attributes: stamp.attributes
    };
    addToCart(cartItem);
  };

  return (
    <div className="stamp-card">
      <div className="stamp-image-container">
        <div className="stamp-image">
          {imageUrl ? (
            <img src={imageUrl} alt={title} />
          ) : (
            <div className="placeholder-image">
              <StampPlaceholder
                category={getCategoryKey(categoryName)}
                title={title}
                size={200}
              />
            </div>
          )}
        </div>
        
        <div className="stamp-actions">
          <button className="action-btn" title={t('stamps.quickView')}>
            <Eye size={16} />
          </button>
          <button className="action-btn" title={t('stamps.addToFavorites')}>
            <Heart size={16} />
          </button>
        </div>
      </div>

      <div className="stamp-info">
        <div className="stamp-category">{categoryName}</div>
        <h3 className="stamp-title">{title}</h3>
        {plainDescription && (
          <p className="stamp-description">{plainDescription}</p>
        )}

        <div className="stamp-price">
          <span className="current-price">{price} DT</span>
        </div>

        <button className="add-to-cart-btn" onClick={handleAddToCart}>
          <ShoppingCart size={16} />
          {t('stamps.addToCart')}
        </button>
      </div>

      <style>{`
        .stamp-card {
          background: var(--white);
          border-radius: 15px;
          overflow: hidden;
          box-shadow: var(--shadow);
          transition: var(--transition);
          position: relative;
        }

        .stamp-card:hover {
          transform: translateY(-8px);
          box-shadow: var(--shadow-hover);
        }

        .stamp-image-container {
          position: relative;
          overflow: hidden;
        }

        .stamp-image {
          width: 100%;
          height: 250px;
          overflow: hidden;
        }

        .stamp-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: var(--transition);
        }

        .stamp-card:hover .stamp-image img {
          transform: scale(1.1);
        }

        .placeholder-image {
          width: 100%;
          height: 100%;
          background: var(--light-gray);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--medium-gray);
          padding: 1rem;
        }

        .stamp-actions {
          position: absolute;
          top: 15px;
          right: 15px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          opacity: 0;
          transition: var(--transition);
        }

        .stamp-card:hover .stamp-actions {
          opacity: 1;
        }

        .action-btn {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: var(--white);
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: var(--transition);
          box-shadow: var(--shadow);
        }

        .action-btn:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: scale(1.1);
        }

        .stamp-info {
          padding: 1.5rem;
        }

        .stamp-category {
          font-size: 0.85rem;
          color: var(--primary-blue);
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 0.5rem;
        }

        .stamp-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--dark-gray);
          margin-bottom: 0.75rem;
          line-height: 1.3;
        }

        .stamp-description {
          font-size: 0.9rem;
          color: var(--medium-gray);
          line-height: 1.5;
          margin-bottom: 1rem;
        }

        .stamp-price {
          margin-bottom: 1.5rem;
        }

        .current-price {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--primary-blue);
        }

        .add-to-cart-btn {
          width: 100%;
          background: var(--primary-blue);
          color: var(--white);
          border: none;
          padding: 12px 20px;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: var(--transition);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        }

        .add-to-cart-btn:hover {
          background: var(--secondary-gold);
          color: var(--primary-blue);
          transform: translateY(-2px);
        }

        .add-to-cart-btn:active {
          transform: translateY(0);
        }
      `}</style>
    </div>
  );
};

export default StampCard;
