PROJET REACT + VITE - LA POSTE TUNISIENNE E-STAMPS
===================================================

Votre projet est maintenant revenu a son etat d'origine React + Vite.

COMMANDES DISPONIBLES:
======================

1. Mode developpement (recommande):
   npm run dev
   
   - Lance le serveur de developpement Vite
   - Accessible sur: http://localhost:5173
   - Hot reload automatique
   - Ideal pour le developpement

2. Construction du projet:
   npm run build
   
   - Cree un build de production dans le dossier "dist"
   - Optimise et minifie le code

3. Previsualisation du build:
   npm run preview
   
   - Lance un serveur pour previsualiser le build de production
   - Accessible sur: http://localhost:4173

4. Verification du code:
   npm run lint
   
   - Verifie la qualite du code avec ESLint

UTILISATION NORMALE:
===================

1. Ouvrez un terminal dans ce dossier
2. Tapez: npm run dev
3. Ouvrez votre navigateur sur: http://localhost:5173
4. Modifiez le code dans src/
5. Les changements apparaissent automatiquement

STRUCTURE DU PROJET:
===================

src/
├── App.jsx              - Composant principal
├── main.jsx             - Point d'entree
├── index.css            - Styles globaux
├── components/          - Composants React
├── pages/               - Pages de l'application
└── assets/              - Images, icones, etc.

public/                  - Fichiers statiques
index.html              - Template HTML principal
vite.config.js          - Configuration Vite
package.json            - Dependances et scripts

NOTES IMPORTANTES:
==================

- Le serveur de developpement utilise le port 5173 (pas 3000)
- Les changements sont automatiquement recharges
- Pour arreter le serveur: Ctrl+C dans le terminal
- Les builds de production sont dans le dossier "dist"

DEPENDANCES INSTALLEES:
======================

- React 19.1.0
- React DOM 19.1.0
- React Router DOM 7.6.2
- Lucide React (icones)
- Vite (bundler)
- ESLint (verification du code)

Votre projet est pret a etre utilise avec les commandes React/Vite standard !
