import React from 'react';

const StampPlaceholder = ({ category, title, size = 250 }) => {
  const getStampDesign = (category) => {
    const designs = {
      heritage: {
        color: '#8B4513',
        icon: '🏛️',
        pattern: 'M10,10 L40,10 L40,40 L10,40 Z',
        bgColor: '#F4E4BC'
      },
      nature: {
        color: '#228B22',
        icon: '🌿',
        pattern: 'M15,15 Q25,5 35,15 Q25,25 15,15',
        bgColor: '#F0FFF0'
      },
      history: {
        color: '#8B0000',
        icon: '📜',
        pattern: 'M20,10 L30,10 L30,40 L20,40 Z',
        bgColor: '#FFF8DC'
      },
      culture: {
        color: '#4B0082',
        icon: '🎭',
        pattern: 'M15,20 Q25,10 35,20 Q25,30 15,20',
        bgColor: '#F8F8FF'
      },
      sports: {
        color: '#FF4500',
        icon: '⚽',
        pattern: 'M20,15 L30,15 L25,25 Z',
        bgColor: '#FFF5EE'
      },
      'special-events': {
        color: '#FFD700',
        icon: '🎉',
        pattern: 'M25,10 L30,20 L20,20 Z',
        bgColor: '#FFFACD'
      }
    };

    return designs[category] || designs.heritage;
  };

  const design = getStampDesign(category);

  return (
    <div className="stamp-placeholder" style={{ width: size, height: size * 1.2 }}>
      <svg width="100%" height="100%" viewBox="0 0 100 120" className="stamp-svg">
        {/* Stamp perforations */}
        <defs>
          <pattern id="perforations" patternUnits="userSpaceOnUse" width="4" height="4">
            <circle cx="2" cy="2" r="1" fill="white"/>
          </pattern>
        </defs>
        
        {/* Main stamp body */}
        <rect x="5" y="5" width="90" height="110" 
              fill={design.bgColor} 
              stroke={design.color} 
              strokeWidth="2"/>
        
        {/* Perforated edges */}
        <rect x="0" y="0" width="100" height="5" fill="url(#perforations)"/>
        <rect x="0" y="115" width="100" height="5" fill="url(#perforations)"/>
        <rect x="0" y="0" width="5" height="120" fill="url(#perforations)"/>
        <rect x="95" y="0" width="5" height="120" fill="url(#perforations)"/>
        
        {/* Decorative border */}
        <rect x="10" y="10" width="80" height="100" 
              fill="none" 
              stroke={design.color} 
              strokeWidth="1" 
              strokeDasharray="2,2"/>
        
        {/* Main icon */}
        <text x="50" y="45" 
              textAnchor="middle" 
              fontSize="20" 
              fill={design.color}>
          {design.icon}
        </text>
        
        {/* Decorative pattern */}
        <path d={design.pattern} 
              fill="none" 
              stroke={design.color} 
              strokeWidth="1" 
              opacity="0.3"/>
        
        {/* Country name */}
        <text x="50" y="65" 
              textAnchor="middle" 
              fontSize="6" 
              fill={design.color} 
              fontWeight="bold">
          TUNISIE
        </text>
        
        {/* Title */}
        <text x="50" y="80" 
              textAnchor="middle" 
              fontSize="4" 
              fill={design.color}>
          {title?.substring(0, 20) || 'TIMBRE'}
        </text>
        
        {/* Value */}
        <text x="50" y="95" 
              textAnchor="middle" 
              fontSize="8" 
              fill={design.color} 
              fontWeight="bold">
          1000
        </text>
        
        <text x="50" y="105" 
              textAnchor="middle" 
              fontSize="4" 
              fill={design.color}>
          MILLIMES
        </text>
      </svg>

      <style>{`
        .stamp-placeholder {
          display: inline-block;
          filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
          transition: transform 0.3s ease;
        }

        .stamp-placeholder:hover {
          transform: scale(1.05);
        }

        .stamp-svg {
          width: 100%;
          height: 100%;
        }
      `}</style>
    </div>
  );
};

export default StampPlaceholder;
