import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import Hero from '../components/Hero';
import CategorySection from '../components/CategorySection';
import ProductGrid from '../components/ProductGrid';
import StampCard from '../components/StampCard';

const Home = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [stamps, setStamps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch stamps from Strapi API
  useEffect(() => {
    const fetchStamps = async () => {
      try {
        setLoading(true);
        const response = await axios.get('http://localhost:1337/api/timbres?populate=*');
        console.log('Stamps data received:', response.data.data);
        setStamps(response.data.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching stamps:', err);
        setError(t('stamps.error'));
      } finally {
        setLoading(false);
      }
    };

    fetchStamps();
  }, []);

  // Sample featured products data (fallback)
  const featuredProducts = [
    {
      id: 1,
      title: "Patrimoine Architectural Tunisien",
      description: "Collection mettant en valeur l'architecture traditionnelle tunisienne",
      price: "15.50",
      originalPrice: "18.00",
      category: "Patrimoine Tunisien",
      isNew: true,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 2,
      title: "Faune Marine de Tunisie",
      description: "Découvrez la richesse marine des côtes tunisiennes",
      price: "12.00",
      category: "Nature & Paysages",
      isNew: false,
      isLimited: true,
      rating: 4,
      inStock: true,
      image: null
    },
    {
      id: 3,
      title: "Artistes Tunisiens Contemporains",
      description: "Hommage aux grands artistes tunisiens modernes",
      price: "20.00",
      category: "Art Contemporain",
      isNew: true,
      isLimited: true,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 4,
      title: "Habib Bourguiba - Père de l'Indépendance",
      description: "Série commémorative dédiée au premier président tunisien",
      price: "25.00",
      category: "Personnalités",
      isNew: false,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 5,
      title: "Jeux Olympiques Paris 2024",
      description: "Participation tunisienne aux Jeux Olympiques",
      price: "18.50",
      category: "Sports",
      isNew: true,
      isLimited: false,
      rating: 4,
      inStock: false,
      image: null
    },
    {
      id: 6,
      title: "Révolution du Jasmin",
      description: "Commémoration de la révolution tunisienne de 2011",
      price: "22.00",
      category: "Événements Spéciaux",
      isNew: false,
      isLimited: true,
      rating: 5,
      inStock: true,
      image: null
    }
  ];

  return (
    <div className="home-page">
      <Hero />
      
      <CategorySection />

      {/* Stamps from Strapi */}
      <section className="stamps-section">
        <div className="container">
          <h2 className="section-title">{t('stamps.title')}</h2>

          {loading && (
            <div className="loading-message">
              <p>{t('stamps.loading')}</p>
            </div>
          )}

          {error && (
            <div className="error-message">
              <p>{error}</p>
            </div>
          )}

          {!loading && !error && stamps.length > 0 && (
            <div className="stamps-grid">
              {stamps.slice(0, 6).map(stamp => (
                <StampCard key={stamp.id} stamp={stamp} />
              ))}
            </div>
          )}

          {!loading && !error && stamps.length === 0 && (
            <div className="no-stamps-message">
              <p>{t('stamps.noStamps')}</p>
            </div>
          )}
        </div>
      </section>

      <ProductGrid
        products={featuredProducts}
        title={t('products.title')}
        showFilters={false}
      />
      
      {/* Statistics Section */}
      <section className="stats-section">
        <div className="container">
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">500+</div>
              <div className="stat-label">Timbres Disponibles</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">50+</div>
              <div className="stat-label">Années d'Histoire</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">10,000+</div>
              <div className="stat-label">Clients Satisfaits</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">25+</div>
              <div className="stat-label">Pays de Livraison</div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="about-preview">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2>À Propos de La Poste Tunisienne</h2>
              <p>
                Depuis plus de 50 ans, La Poste Tunisienne préserve et célèbre le patrimoine 
                culturel tunisien à travers ses émissions philatéliques. Notre boutique en ligne 
                vous offre un accès privilégié à nos collections exclusives.
              </p>
              <p>
                Chaque timbre raconte une histoire, capture un moment de notre riche héritage 
                et témoigne de la beauté de notre pays. Découvrez l'art postal tunisien dans 
                toute sa splendeur.
              </p>
              <button className="btn btn-primary" onClick={() => navigate('/about')}>
                {t('hero.learnMore')}
              </button>
            </div>
            <div className="about-image">
              <div className="image-placeholder">
                <span className="placeholder-icon">🏛️</span>
                <p>Siège de La Poste Tunisienne</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <style>{`
        .home-page {
          flex: 1;
        }

        .stamps-section {
          padding: 4rem 0;
          background: var(--light-gray);
        }

        .section-title {
          font-size: 2.5rem;
          color: var(--primary-blue);
          text-align: center;
          margin-bottom: 3rem;
          font-weight: 700;
        }

        .stamps-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-bottom: 2rem;
        }

        .loading-message,
        .error-message,
        .no-stamps-message {
          text-align: center;
          padding: 2rem;
          font-size: 1.1rem;
        }

        .loading-message p {
          color: var(--medium-gray);
        }

        .error-message p {
          color: #dc3545;
          font-weight: 600;
        }

        .no-stamps-message p {
          color: var(--medium-gray);
        }

        .stats-section {
          background: var(--primary-blue);
          color: var(--white);
          padding: 4rem 0;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 2rem;
          text-align: center;
        }

        .stat-item {
          padding: 1rem;
        }

        .stat-number {
          font-size: 3rem;
          font-weight: 700;
          color: var(--secondary-gold);
          margin-bottom: 0.5rem;
        }

        .stat-label {
          font-size: 1.1rem;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }

        .about-preview {
          padding: 4rem 0;
          background: var(--white);
        }

        .about-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 4rem;
          align-items: center;
        }

        .about-text h2 {
          font-size: 2.5rem;
          color: var(--primary-blue);
          margin-bottom: 2rem;
        }

        .about-text p {
          font-size: 1.1rem;
          line-height: 1.8;
          color: var(--medium-gray);
          margin-bottom: 1.5rem;
        }

        .about-image {
          display: flex;
          justify-content: center;
        }

        .image-placeholder {
          width: 100%;
          max-width: 400px;
          height: 300px;
          background: linear-gradient(135deg, var(--light-gray), var(--border-color));
          border-radius: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 3px dashed var(--primary-blue);
          text-align: center;
        }

        .placeholder-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
          opacity: 0.7;
        }

        .image-placeholder p {
          color: var(--primary-blue);
          font-weight: 600;
          margin: 0;
        }

        @media (max-width: 768px) {
          .stamps-section {
            padding: 3rem 0;
          }

          .section-title {
            font-size: 2rem;
            margin-bottom: 2rem;
          }

          .stamps-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
          }

          .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
          }

          .stat-number {
            font-size: 2.5rem;
          }

          .about-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            text-align: center;
          }

          .about-text h2 {
            font-size: 2rem;
          }

          .about-text p {
            font-size: 1rem;
          }
        }

        @media (max-width: 480px) {
          .stamps-section {
            padding: 2rem 0;
          }

          .section-title {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
          }

          .stamps-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .stats-section {
            padding: 3rem 0;
          }

          .stats-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .stat-number {
            font-size: 2rem;
          }

          .about-preview {
            padding: 3rem 0;
          }

          .about-text h2 {
            font-size: 1.8rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Home;
