import React from 'react';
import { ArrowRight, Star, Package, Truck } from 'lucide-react';

const Hero = () => {
  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Bienvenue à la Boutique Philatélique
              <span className="highlight"> Tunisienne</span>
            </h1>
            <p className="hero-description">
              Découvrez notre collection exclusive de timbres tunisiens et produits philatéliques. 
              Commandez en ligne et recevez vos timbres directement chez vous.
            </p>
            
            <div className="hero-features">
              <div className="feature">
                <Star className="feature-icon" />
                <span>Collection Exclusive</span>
              </div>
              <div className="feature">
                <Package className="feature-icon" />
                <span>Produits Authentiques</span>
              </div>
              <div className="feature">
                <Truck className="feature-icon" />
                <span>Livraison Rapide</span>
              </div>
            </div>

            <div className="hero-actions">
              <button className="btn btn-primary">
                Découvrir la Collection
                <ArrowRight size={20} />
              </button>
              <button className="btn btn-outline">
                Nouveautés 2024
              </button>
            </div>
          </div>

          <div className="hero-image">
            <div className="stamp-showcase">
              <div className="stamp-card featured">
                <div className="stamp-image">
                  <div className="stamp-placeholder">
                    🏛️
                  </div>
                </div>
                <div className="stamp-info">
                  <h4>Patrimoine Tunisien</h4>
                  <p>Série 2024</p>
                </div>
              </div>
              
              <div className="stamp-card">
                <div className="stamp-image">
                  <div className="stamp-placeholder">
                    🌊
                  </div>
                </div>
                <div className="stamp-info">
                  <h4>Côtes Tunisiennes</h4>
                  <p>Collection Nature</p>
                </div>
              </div>
              
              <div className="stamp-card">
                <div className="stamp-image">
                  <div className="stamp-placeholder">
                    🎨
                  </div>
                </div>
                <div className="stamp-info">
                  <h4>Art Contemporain</h4>
                  <p>Édition Limitée</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .hero {
          background: linear-gradient(135deg, var(--primary-blue) 0%, #004080 100%);
          color: var(--white);
          padding: 4rem 0;
          position: relative;
          overflow: hidden;
        }

        .hero::before {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 50%;
          height: 100%;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stamp" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,215,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stamp)"/></svg>');
          opacity: 0.3;
        }

        .hero-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 4rem;
          align-items: center;
          position: relative;
          z-index: 1;
        }

        .hero-title {
          font-size: 3rem;
          font-weight: 700;
          line-height: 1.2;
          margin-bottom: 1.5rem;
          color: var(--white);
        }

        .highlight {
          color: var(--secondary-gold);
        }

        .hero-description {
          font-size: 1.2rem;
          line-height: 1.6;
          margin-bottom: 2rem;
          color: rgba(255, 255, 255, 0.9);
        }

        .hero-features {
          display: flex;
          gap: 2rem;
          margin-bottom: 2rem;
        }

        .feature {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--secondary-gold);
          font-weight: 500;
        }

        .feature-icon {
          width: 20px;
          height: 20px;
        }

        .hero-actions {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;
        }

        .hero-actions .btn {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .hero-actions .btn-primary {
          background: var(--secondary-gold);
          color: var(--primary-blue);
        }

        .hero-actions .btn-primary:hover {
          background: #E6C200;
        }

        .hero-actions .btn-outline {
          border-color: var(--white);
          color: var(--white);
        }

        .hero-actions .btn-outline:hover {
          background: var(--white);
          color: var(--primary-blue);
        }

        .stamp-showcase {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.5rem;
          position: relative;
        }

        .stamp-card {
          background: var(--white);
          border-radius: 15px;
          padding: 1.5rem;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
          transition: var(--transition);
          color: var(--dark-gray);
        }

        .stamp-card.featured {
          grid-column: 1 / -1;
          transform: scale(1.1);
        }

        .stamp-card:hover {
          transform: translateY(-10px) scale(1.05);
        }

        .stamp-card.featured:hover {
          transform: translateY(-10px) scale(1.15);
        }

        .stamp-image {
          margin-bottom: 1rem;
        }

        .stamp-placeholder {
          width: 100%;
          height: 120px;
          background: linear-gradient(135deg, var(--light-gray), var(--border-color));
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
          border: 3px dashed var(--primary-blue);
        }

        .stamp-info h4 {
          color: var(--primary-blue);
          margin-bottom: 0.5rem;
          font-size: 1.1rem;
        }

        .stamp-info p {
          color: var(--medium-gray);
          font-size: 0.9rem;
          margin: 0;
        }

        @media (max-width: 768px) {
          .hero {
            padding: 3rem 0;
          }

          .hero-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            text-align: center;
          }

          .hero-title {
            font-size: 2.5rem;
          }

          .hero-features {
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
          }

          .hero-actions {
            justify-content: center;
          }

          .stamp-showcase {
            grid-template-columns: 1fr;
          }

          .stamp-card.featured {
            grid-column: 1;
            transform: none;
          }
        }

        @media (max-width: 480px) {
          .hero-title {
            font-size: 2rem;
          }

          .hero-description {
            font-size: 1rem;
          }

          .hero-features {
            flex-direction: column;
            align-items: center;
          }

          .hero-actions {
            flex-direction: column;
            align-items: center;
          }

          .hero-actions .btn {
            width: 100%;
            max-width: 250px;
          }
        }
      `}</style>
    </section>
  );
};

export default Hero;
