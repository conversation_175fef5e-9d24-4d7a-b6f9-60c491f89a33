import React from 'react';
import { Award, Users, Globe, Heart } from 'lucide-react';

const About = () => {
  return (
    <div className="about-page">
      {/* Hero Section */}
      <section className="about-hero">
        <div className="container">
          <div className="hero-content">
            <h1>À Propos de La Poste Tunisienne</h1>
            <p>
              Depuis plus de 50 ans, nous préservons et célébrons le patrimoine culturel 
              tunisien à travers nos émissions philatéliques exceptionnelles.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="mission-section">
        <div className="container">
          <div className="mission-content">
            <div className="mission-text">
              <h2>Notre Mission</h2>
              <p>
                La Poste Tunisienne s'engage à préserver et promouvoir le riche patrimoine 
                culturel de la Tunisie à travers l'art philatélique. Chaque timbre que nous 
                émettons raconte une histoire unique de notre nation.
              </p>
              <p>
                Notre boutique en ligne offre aux collectionneurs du monde entier un accès 
                privilégié à nos collections exclusives, permettant de découvrir la beauté 
                et la diversité de la culture tunisienne.
              </p>
            </div>
            <div className="mission-image">
              <div className="image-placeholder">
                <span className="placeholder-icon">🎯</span>
                <p>Notre Mission</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="values-section">
        <div className="container">
          <h2 className="section-title">Nos Valeurs</h2>
          <div className="values-grid">
            <div className="value-card">
              <div className="value-icon">
                <Award size={40} />
              </div>
              <h3>Excellence</h3>
              <p>
                Nous nous engageons à maintenir les plus hauts standards de qualité 
                dans toutes nos émissions philatéliques.
              </p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <Heart size={40} />
              </div>
              <h3>Passion</h3>
              <p>
                Notre amour pour l'art postal et le patrimoine tunisien guide 
                chacune de nos créations.
              </p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <Users size={40} />
              </div>
              <h3>Communauté</h3>
              <p>
                Nous cultivons une communauté mondiale de passionnés de philatélie 
                et d'amoureux de la culture tunisienne.
              </p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <Globe size={40} />
              </div>
              <h3>Rayonnement</h3>
              <p>
                Nous faisons rayonner la culture tunisienne à travers le monde 
                grâce à nos timbres d'exception.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* History Section */}
      <section className="history-section">
        <div className="container">
          <h2 className="section-title">Notre Histoire</h2>
          <div className="timeline">
            <div className="timeline-item">
              <div className="timeline-year">1956</div>
              <div className="timeline-content">
                <h3>Création de La Poste Tunisienne</h3>
                <p>Fondation de l'institution postale nationale après l'indépendance.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-year">1960</div>
              <div className="timeline-content">
                <h3>Premières Émissions</h3>
                <p>Lancement des premières séries de timbres tunisiens indépendants.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-year">1990</div>
              <div className="timeline-content">
                <h3>Modernisation</h3>
                <p>Introduction de nouvelles techniques d'impression et de design.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-year">2010</div>
              <div className="timeline-content">
                <h3>Boutique en Ligne</h3>
                <p>Lancement de la première plateforme e-commerce philatélique.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-year">2024</div>
              <div className="timeline-content">
                <h3>Nouvelle Ère Digitale</h3>
                <p>Refonte complète de l'expérience client en ligne.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="team-section">
        <div className="container">
          <h2 className="section-title">Notre Équipe</h2>
          <p className="section-description">
            Une équipe passionnée de professionnels dédiés à l'excellence philatélique
          </p>
          <div className="team-grid">
            <div className="team-member">
              <div className="member-photo">
                <span className="photo-placeholder">👨‍💼</span>
              </div>
              <h3>Ahmed Ben Salem</h3>
              <p className="member-role">Directeur Philatélique</p>
              <p className="member-description">
                Expert en philatélie avec plus de 20 ans d'expérience dans le secteur postal.
              </p>
            </div>
            <div className="team-member">
              <div className="member-photo">
                <span className="photo-placeholder">👩‍🎨</span>
              </div>
              <h3>Leila Mansouri</h3>
              <p className="member-role">Directrice Artistique</p>
              <p className="member-description">
                Artiste renommée spécialisée dans le design de timbres et l'art postal.
              </p>
            </div>
            <div className="team-member">
              <div className="member-photo">
                <span className="photo-placeholder">👨‍💻</span>
              </div>
              <h3>Karim Trabelsi</h3>
              <p className="member-role">Responsable E-commerce</p>
              <p className="member-description">
                Expert en commerce électronique et expérience utilisateur digitale.
              </p>
            </div>
          </div>
        </div>
      </section>

      <style>{`
        .about-page {
          flex: 1;
        }

        .about-hero {
          background: linear-gradient(135deg, var(--primary-blue), #004080);
          color: var(--white);
          padding: 4rem 0;
          text-align: center;
        }

        .hero-content h1 {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1.5rem;
          color: var(--white);
        }

        .hero-content p {
          font-size: 1.3rem;
          color: rgba(255, 255, 255, 0.9);
          max-width: 800px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .mission-section {
          padding: 4rem 0;
          background: var(--white);
        }

        .mission-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 4rem;
          align-items: center;
        }

        .mission-text h2 {
          font-size: 2.5rem;
          color: var(--primary-blue);
          margin-bottom: 2rem;
        }

        .mission-text p {
          font-size: 1.1rem;
          line-height: 1.8;
          color: var(--medium-gray);
          margin-bottom: 1.5rem;
        }

        .mission-image {
          display: flex;
          justify-content: center;
        }

        .image-placeholder {
          width: 100%;
          max-width: 400px;
          height: 300px;
          background: linear-gradient(135deg, var(--light-gray), var(--border-color));
          border-radius: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 3px dashed var(--primary-blue);
          text-align: center;
        }

        .placeholder-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
          opacity: 0.7;
        }

        .image-placeholder p {
          color: var(--primary-blue);
          font-weight: 600;
          margin: 0;
        }

        .values-section {
          padding: 4rem 0;
          background: var(--light-gray);
        }

        .section-title {
          text-align: center;
          font-size: 2.5rem;
          color: var(--primary-blue);
          margin-bottom: 3rem;
        }

        .values-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        }

        .value-card {
          background: var(--white);
          padding: 2rem;
          border-radius: 15px;
          text-align: center;
          box-shadow: var(--shadow);
          transition: var(--transition);
        }

        .value-card:hover {
          transform: translateY(-5px);
          box-shadow: var(--shadow-hover);
        }

        .value-icon {
          width: 80px;
          height: 80px;
          background: var(--primary-blue);
          color: var(--white);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
        }

        .value-card h3 {
          color: var(--primary-blue);
          margin-bottom: 1rem;
          font-size: 1.3rem;
        }

        .value-card p {
          color: var(--medium-gray);
          line-height: 1.6;
        }

        .history-section {
          padding: 4rem 0;
          background: var(--white);
        }

        .timeline {
          max-width: 800px;
          margin: 0 auto;
          position: relative;
        }

        .timeline::before {
          content: '';
          position: absolute;
          left: 50%;
          top: 0;
          bottom: 0;
          width: 2px;
          background: var(--secondary-gold);
          transform: translateX(-50%);
        }

        .timeline-item {
          display: flex;
          align-items: center;
          margin-bottom: 3rem;
          position: relative;
        }

        .timeline-item:nth-child(odd) {
          flex-direction: row;
        }

        .timeline-item:nth-child(even) {
          flex-direction: row-reverse;
        }

        .timeline-year {
          background: var(--primary-blue);
          color: var(--white);
          padding: 1rem 1.5rem;
          border-radius: 25px;
          font-weight: 700;
          font-size: 1.1rem;
          min-width: 100px;
          text-align: center;
          position: relative;
          z-index: 2;
        }

        .timeline-content {
          flex: 1;
          padding: 0 2rem;
        }

        .timeline-content h3 {
          color: var(--primary-blue);
          margin-bottom: 0.5rem;
          font-size: 1.3rem;
        }

        .timeline-content p {
          color: var(--medium-gray);
          line-height: 1.6;
        }

        .team-section {
          padding: 4rem 0;
          background: var(--light-gray);
        }

        .section-description {
          text-align: center;
          font-size: 1.2rem;
          color: var(--medium-gray);
          margin-bottom: 3rem;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }

        .team-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
        }

        .team-member {
          background: var(--white);
          padding: 2rem;
          border-radius: 15px;
          text-align: center;
          box-shadow: var(--shadow);
          transition: var(--transition);
        }

        .team-member:hover {
          transform: translateY(-5px);
          box-shadow: var(--shadow-hover);
        }

        .member-photo {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: var(--light-gray);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
          border: 4px solid var(--secondary-gold);
        }

        .photo-placeholder {
          font-size: 3rem;
        }

        .team-member h3 {
          color: var(--primary-blue);
          margin-bottom: 0.5rem;
          font-size: 1.3rem;
        }

        .member-role {
          color: var(--secondary-gold);
          font-weight: 600;
          margin-bottom: 1rem;
          text-transform: uppercase;
          font-size: 0.9rem;
        }

        .member-description {
          color: var(--medium-gray);
          line-height: 1.6;
          font-size: 0.95rem;
        }

        @media (max-width: 768px) {
          .about-hero {
            padding: 3rem 0;
          }

          .hero-content h1 {
            font-size: 2.5rem;
          }

          .hero-content p {
            font-size: 1.1rem;
          }

          .mission-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            text-align: center;
          }

          .mission-text h2 {
            font-size: 2rem;
          }

          .section-title {
            font-size: 2rem;
          }

          .timeline::before {
            left: 30px;
          }

          .timeline-item {
            flex-direction: row !important;
            padding-left: 60px;
          }

          .timeline-year {
            position: absolute;
            left: 0;
            min-width: 80px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
          }

          .timeline-content {
            padding: 0;
          }
        }

        @media (max-width: 480px) {
          .hero-content h1 {
            font-size: 2rem;
          }

          .mission-text h2 {
            font-size: 1.8rem;
          }

          .values-grid {
            grid-template-columns: 1fr;
          }

          .team-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default About;
