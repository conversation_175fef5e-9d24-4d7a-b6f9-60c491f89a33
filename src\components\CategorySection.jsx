import React from 'react';
import { ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const CategorySection = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();

  const handleCategoryClick = (categoryId) => {
    navigate(`/categories?category=${categoryId}`);
  };

  const categories = [
    {
      id: 1,
      name: 'Patrim<PERSON><PERSON> Tunis<PERSON>',
      description: 'Découvrez l\'histoire et la culture tunisienne',
      icon: '🏛️',
      count: 45,
      color: 'var(--primary-blue)'
    },
    {
      id: 2,
      name: 'Nature & Paysages',
      description: 'La beauté naturelle de la Tunisie',
      icon: '🌊',
      count: 32,
      color: '#2E8B57'
    },
    {
      id: 3,
      name: 'Art Contemporain',
      description: 'Œuvres d\'artistes tunisiens modernes',
      icon: '🎨',
      count: 28,
      color: '#8B4513'
    },
    {
      id: 4,
      name: 'Personnalités',
      description: 'Figures emblématiques tunisiennes',
      icon: '👤',
      count: 38,
      color: '#4B0082'
    },
    {
      id: 5,
      name: 'Événements Spéciaux',
      description: 'Commémorations et célébrations',
      icon: '🎉',
      count: 22,
      color: '#DC143C'
    },
    {
      id: 6,
      name: 'Sports',
      description: 'Champions et événements sportifs',
      icon: '⚽',
      count: 19,
      color: '#FF8C00'
    }
  ];

  return (
    <section className="category-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">{t('categories.title')}</h2>
          <p className="section-description">
            Découvrez notre vaste gamme de timbres organisés par thématiques
          </p>
        </div>

        <div className="categories-grid">
          {categories.map(category => (
            <div key={category.id} className="category-card">
              <div className="category-icon" style={{ backgroundColor: category.color }}>
                <span className="icon">{category.icon}</span>
              </div>
              
              <div className="category-content">
                <h3 className="category-name">{category.name}</h3>
                <p className="category-description">{category.description}</p>
                <div className="category-count">{category.count} timbres</div>
              </div>

              <div className="category-action">
                <button
                  className="explore-btn"
                  onClick={() => handleCategoryClick(category.id)}
                >
                  Explorer
                  <ArrowRight size={16} />
                </button>
              </div>

              <div className="category-overlay"></div>
            </div>
          ))}
        </div>

        <div className="section-footer">
          <button
            className="btn btn-outline"
            onClick={() => navigate('/categories')}
          >
            Voir toutes les catégories
          </button>
        </div>
      </div>

      <style>{`
        .category-section {
          padding: 4rem 0;
          background: var(--light-gray);
        }

        .section-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .section-title {
          font-size: 2.5rem;
          color: var(--primary-blue);
          margin-bottom: 1rem;
        }

        .section-description {
          font-size: 1.2rem;
          color: var(--medium-gray);
          max-width: 600px;
          margin: 0 auto;
        }

        .categories-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
          margin-bottom: 3rem;
        }

        .category-card {
          background: var(--white);
          border-radius: 20px;
          padding: 2rem;
          position: relative;
          overflow: hidden;
          transition: var(--transition);
          cursor: pointer;
          border: 2px solid transparent;
        }

        .category-card:hover {
          transform: translateY(-10px);
          box-shadow: var(--shadow-hover);
          border-color: var(--secondary-gold);
        }

        .category-card:hover .category-overlay {
          opacity: 1;
        }

        .category-card:hover .explore-btn {
          opacity: 1;
          transform: translateY(0);
        }

        .category-icon {
          width: 80px;
          height: 80px;
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1.5rem;
          position: relative;
          z-index: 2;
        }

        .icon {
          font-size: 2.5rem;
          filter: brightness(0) invert(1);
        }

        .category-content {
          position: relative;
          z-index: 2;
        }

        .category-name {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--primary-blue);
          margin-bottom: 0.75rem;
        }

        .category-description {
          color: var(--medium-gray);
          line-height: 1.6;
          margin-bottom: 1rem;
        }

        .category-count {
          color: var(--secondary-gold);
          font-weight: 600;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .category-action {
          position: absolute;
          bottom: 2rem;
          right: 2rem;
          z-index: 3;
        }

        .explore-btn {
          background: var(--primary-blue);
          color: var(--white);
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 25px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          transition: var(--transition);
          opacity: 0;
          transform: translateY(20px);
        }

        .explore-btn:hover {
          background: #004080;
          transform: translateY(-2px);
        }

        .category-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, var(--primary-blue), var(--secondary-gold));
          opacity: 0;
          transition: var(--transition);
          z-index: 1;
        }

        .section-footer {
          text-align: center;
        }

        .section-footer .btn {
          padding: 1rem 2rem;
          font-size: 1.1rem;
        }

        @media (max-width: 768px) {
          .category-section {
            padding: 3rem 0;
          }

          .section-title {
            font-size: 2rem;
          }

          .section-description {
            font-size: 1rem;
          }

          .categories-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }

          .category-card {
            padding: 1.5rem;
          }

          .category-icon {
            width: 60px;
            height: 60px;
          }

          .icon {
            font-size: 2rem;
          }

          .category-name {
            font-size: 1.3rem;
          }

          .explore-btn {
            opacity: 1;
            transform: translateY(0);
            position: static;
            margin-top: 1rem;
          }

          .category-action {
            position: static;
          }
        }

        @media (max-width: 480px) {
          .categories-grid {
            grid-template-columns: 1fr;
          }

          .category-card {
            padding: 1rem;
          }

          .section-footer .btn {
            width: 100%;
            max-width: 300px;
          }
        }
      `}</style>
    </section>
  );
};

export default CategorySection;
