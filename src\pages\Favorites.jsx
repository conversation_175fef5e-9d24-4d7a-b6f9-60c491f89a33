import React from 'react';
import { Heart, ShoppingBag } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useFavorites } from '../contexts/FavoritesContext';
import { useNavigate } from 'react-router-dom';
import StampCard from '../components/StampCard';

const Favorites = () => {
  const { t } = useLanguage();
  const { favorites, getFavoritesCount } = useFavorites();
  const navigate = useNavigate();

  const handleContinueShopping = () => {
    navigate('/timbres');
  };

  return (
    <div className="favorites-page">
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">
            <Heart size={32} />
            {t('favorites.title')}
          </h1>
          <p className="page-description">
            {t('favorites.description')}
          </p>
        </div>
      </div>

      <div className="container">
        {favorites.length === 0 ? (
          <div className="empty-favorites">
            <div className="empty-icon">
              <Heart size={64} />
            </div>
            <h2>{t('favorites.empty')}</h2>
            <p>{t('favorites.emptyDescription')}</p>
            <button className="btn btn-primary" onClick={handleContinueShopping}>
              <ShoppingBag size={20} />
              {t('favorites.continueShopping')}
            </button>
          </div>
        ) : (
          <>
            <div className="favorites-header">
              <h2>{getFavoritesCount()} {t('favorites.itemsCount')}</h2>
            </div>
            
            <div className="favorites-grid">
              {favorites.map(stamp => (
                <StampCard key={stamp.id} stamp={stamp} />
              ))}
            </div>
          </>
        )}
      </div>

      <style>{`
        .favorites-page {
          min-height: 100vh;
          background: var(--light-gray);
        }

        .page-header {
          background: var(--primary-blue);
          color: var(--white);
          padding: 4rem 0 2rem;
          text-align: center;
        }

        .page-title {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: var(--white);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
        }

        .page-description {
          font-size: 1.2rem;
          color: rgba(255, 255, 255, 0.9);
          max-width: 600px;
          margin: 0 auto;
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
        }

        .empty-favorites {
          text-align: center;
          padding: 4rem 2rem;
          background: var(--white);
          border-radius: 15px;
          margin: 3rem 0;
          box-shadow: var(--shadow);
        }

        .empty-icon {
          color: var(--medium-gray);
          margin-bottom: 2rem;
        }

        .empty-favorites h2 {
          font-size: 2rem;
          color: var(--dark-gray);
          margin-bottom: 1rem;
        }

        .empty-favorites p {
          font-size: 1.1rem;
          color: var(--medium-gray);
          margin-bottom: 2rem;
          max-width: 400px;
          margin-left: auto;
          margin-right: auto;
        }

        .favorites-header {
          padding: 3rem 0 2rem;
        }

        .favorites-header h2 {
          font-size: 1.5rem;
          color: var(--primary-blue);
          margin: 0;
        }

        .favorites-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          padding-bottom: 3rem;
        }

        .btn {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem 2rem;
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: var(--transition);
          text-decoration: none;
        }

        .btn-primary {
          background: var(--primary-blue);
          color: var(--white);
        }

        .btn-primary:hover {
          background: var(--secondary-gold);
          color: var(--primary-blue);
          transform: translateY(-2px);
        }

        @media (max-width: 768px) {
          .page-header {
            padding: 3rem 0 1.5rem;
          }

          .page-title {
            font-size: 2.5rem;
          }

          .page-description {
            font-size: 1.1rem;
          }

          .favorites-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
          }

          .empty-favorites {
            padding: 3rem 1.5rem;
            margin: 2rem 0;
          }
        }

        @media (max-width: 480px) {
          .page-header {
            padding: 2rem 0 1rem;
          }

          .page-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
          }

          .favorites-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Favorites;
