import React from 'react';
import { X, ShoppingC<PERSON>, Heart, Share2, ZoomIn } from 'lucide-react';
import { useModal } from '../contexts/ModalContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useCart } from '../contexts/CartContext';
import { useFavorites } from '../contexts/FavoritesContext';
import { useNotification } from '../contexts/NotificationContext';
import StampPlaceholder from './StampPlaceholder';

const Modal = () => {
  const { isModalOpen, modalContent, modalType, closeModal } = useModal();
  const { t } = useLanguage();
  const { addToCart } = useCart();
  const { toggleFavorite, isFavorite } = useFavorites();
  const { showSuccess } = useNotification();

  if (!isModalOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      closeModal();
    }
  };

  const renderQuickView = () => {
    if (!modalContent || !modalContent.attributes) return null;

    const { id, attributes } = modalContent;
    const {
      nom: title,
      prix: price,
      image,
      categorie,
      description
    } = attributes;

    const imageUrl = image?.data?.attributes?.url 
      ? `http://localhost:1337${image.data.attributes.url}`
      : null;

    const categoryName = categorie?.data?.attributes?.name || t('stamps.category');

    const getPlainTextDescription = (desc) => {
      if (!desc || !Array.isArray(desc)) return '';
      return desc
        .map(block => 
          block.children?.map(child => child.text).join(' ') || ''
        )
        .join(' ');
    };

    const plainDescription = getPlainTextDescription(description);

    const getCategoryKey = (catName) => {
      if (!catName) return 'heritage';
      const name = catName.toLowerCase();
      if (name.includes('patrimoine') || name.includes('heritage')) return 'heritage';
      if (name.includes('nature') || name.includes('paysage')) return 'nature';
      if (name.includes('histoire') || name.includes('history')) return 'history';
      if (name.includes('culture') || name.includes('art')) return 'culture';
      if (name.includes('sport')) return 'sports';
      if (name.includes('événement') || name.includes('special')) return 'special-events';
      return 'heritage';
    };

    const handleAddToCart = () => {
      const cartItem = {
        id: modalContent.id,
        title,
        price,
        image: imageUrl,
        category: categoryName,
        attributes: modalContent.attributes
      };
      addToCart(cartItem);
      showSuccess(t('notifications.addedToCart'));
    };

    const handleToggleFavorite = () => {
      const favoriteItem = {
        id: modalContent.id,
        title,
        price,
        image: imageUrl,
        category: categoryName,
        attributes: modalContent.attributes
      };
      toggleFavorite(favoriteItem);
      const message = isFavorite(modalContent.id) 
        ? t('notifications.removedFromFavorites')
        : t('notifications.addedToFavorites');
      showSuccess(message);
    };

    const handleShare = () => {
      if (navigator.share) {
        navigator.share({
          title: title,
          text: plainDescription,
          url: window.location.href
        });
      } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href);
        showSuccess(t('notifications.linkCopied'));
      }
    };

    return (
      <div className="quick-view-content">
        <div className="quick-view-image">
          {imageUrl ? (
            <img src={imageUrl} alt={title} />
          ) : (
            <StampPlaceholder 
              category={getCategoryKey(categoryName)} 
              title={title}
              size={300}
            />
          )}
        </div>
        
        <div className="quick-view-details">
          <div className="stamp-category">{categoryName}</div>
          <h2 className="stamp-title">{title}</h2>
          
          {plainDescription && (
            <p className="stamp-description">{plainDescription}</p>
          )}
          
          <div className="stamp-price">
            <span className="current-price">{price} DT</span>
          </div>
          
          <div className="stamp-actions">
            <button className="btn btn-primary" onClick={handleAddToCart}>
              <ShoppingCart size={20} />
              {t('stamps.addToCart')}
            </button>
            
            <button 
              className={`btn btn-outline ${isFavorite(id) ? 'active' : ''}`}
              onClick={handleToggleFavorite}
            >
              <Heart size={20} fill={isFavorite(id) ? 'currentColor' : 'none'} />
              {isFavorite(id) ? t('stamps.removeFromFavorites') : t('stamps.addToFavorites')}
            </button>
            
            <button className="btn btn-outline" onClick={handleShare}>
              <Share2 size={20} />
              {t('stamps.share')}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content">
        <button className="modal-close" onClick={closeModal}>
          <X size={24} />
        </button>
        
        {modalType === 'quickView' && renderQuickView()}
      </div>

      <style>{`
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          z-index: 3000;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
        }

        .modal-content {
          background: var(--white);
          border-radius: 15px;
          max-width: 800px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
          position: relative;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-close {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: var(--white);
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: var(--shadow);
          z-index: 10;
          transition: var(--transition);
        }

        .modal-close:hover {
          background: var(--light-gray);
        }

        .quick-view-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
          padding: 2rem;
        }

        .quick-view-image {
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--light-gray);
          border-radius: 10px;
          padding: 2rem;
        }

        .quick-view-image img {
          max-width: 100%;
          max-height: 400px;
          object-fit: contain;
          border-radius: 8px;
        }

        .quick-view-details {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .stamp-category {
          font-size: 0.9rem;
          color: var(--primary-blue);
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stamp-title {
          font-size: 2rem;
          font-weight: 700;
          color: var(--dark-gray);
          margin: 0;
        }

        .stamp-description {
          color: var(--medium-gray);
          line-height: 1.6;
          margin: 0;
        }

        .stamp-price {
          margin: 1rem 0;
        }

        .current-price {
          font-size: 2rem;
          font-weight: 700;
          color: var(--primary-blue);
        }

        .stamp-actions {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-top: auto;
        }

        .stamp-actions .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 1rem;
          border-radius: 8px;
          font-weight: 600;
          transition: var(--transition);
          cursor: pointer;
        }

        .btn-primary {
          background: var(--primary-blue);
          color: var(--white);
          border: none;
        }

        .btn-primary:hover {
          background: var(--secondary-gold);
          color: var(--primary-blue);
        }

        .btn-outline {
          background: transparent;
          color: var(--primary-blue);
          border: 2px solid var(--primary-blue);
        }

        .btn-outline:hover {
          background: var(--primary-blue);
          color: var(--white);
        }

        .btn-outline.active {
          background: var(--primary-blue);
          color: var(--white);
        }

        @media (max-width: 768px) {
          .modal-overlay {
            padding: 1rem;
          }

          .quick-view-content {
            grid-template-columns: 1fr;
            gap: 1.5rem;
            padding: 1.5rem;
          }

          .stamp-title {
            font-size: 1.5rem;
          }

          .current-price {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Modal;
