import React from 'react';
import { X, Plus, Minus, Trash2 } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
import { useLanguage } from '../contexts/LanguageContext';

const Cart = () => {
  const { 
    cartItems, 
    isCartOpen, 
    closeCart, 
    updateQuantity, 
    removeFromCart, 
    getCartTotal,
    clearCart 
  } = useCart();
  const { t } = useLanguage();

  if (!isCartOpen) return null;

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  return (
    <div className="cart-overlay">
      <div className="cart-sidebar">
        <div className="cart-header">
          <h2>{t('nav.cart')}</h2>
          <button className="close-btn" onClick={closeCart}>
            <X size={24} />
          </button>
        </div>

        <div className="cart-content">
          {cartItems.length === 0 ? (
            <div className="empty-cart">
              <p>{t('cart.empty')}</p>
            </div>
          ) : (
            <>
              <div className="cart-items">
                {cartItems.map(item => (
                  <div key={item.id} className="cart-item">
                    <div className="item-image">
                      {item.image ? (
                        <img src={item.image} alt={item.title} />
                      ) : (
                        <div className="placeholder">📮</div>
                      )}
                    </div>
                    
                    <div className="item-details">
                      <h4>{item.title}</h4>
                      <p className="item-category">{item.category}</p>
                      <p className="item-price">{item.attributes?.prix || item.price} DT</p>
                    </div>

                    <div className="item-controls">
                      <div className="quantity-controls">
                        <button 
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="qty-btn"
                        >
                          <Minus size={16} />
                        </button>
                        <span className="quantity">{item.quantity}</span>
                        <button 
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="qty-btn"
                        >
                          <Plus size={16} />
                        </button>
                      </div>
                      
                      <button 
                        onClick={() => removeFromCart(item.id)}
                        className="remove-btn"
                        title={t('common.delete')}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="cart-footer">
                <div className="cart-total">
                  <strong>{t('cart.total')}: {getCartTotal().toFixed(2)} DT</strong>
                </div>
                
                <div className="cart-actions">
                  <button className="clear-btn" onClick={clearCart}>
                    {t('cart.clear')}
                  </button>
                  <button className="checkout-btn">
                    {t('cart.checkout')}
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      <style>{`
        .cart-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          z-index: 2000;
          display: flex;
          justify-content: flex-end;
        }

        .cart-sidebar {
          width: 400px;
          max-width: 90vw;
          background: var(--white);
          height: 100vh;
          overflow-y: auto;
          box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
          display: flex;
          flex-direction: column;
        }

        .cart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1.5rem;
          border-bottom: 1px solid var(--border-color);
          background: var(--primary-blue);
          color: var(--white);
        }

        .cart-header h2 {
          margin: 0;
          font-size: 1.5rem;
        }

        .close-btn {
          background: none;
          border: none;
          color: var(--white);
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 4px;
          transition: var(--transition);
        }

        .close-btn:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .cart-content {
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .empty-cart {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--medium-gray);
          font-size: 1.1rem;
        }

        .cart-items {
          flex: 1;
          padding: 1rem;
        }

        .cart-item {
          display: flex;
          gap: 1rem;
          padding: 1rem 0;
          border-bottom: 1px solid var(--border-color);
        }

        .item-image {
          width: 60px;
          height: 60px;
          border-radius: 8px;
          overflow: hidden;
          background: var(--light-gray);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .item-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .placeholder {
          font-size: 1.5rem;
        }

        .item-details {
          flex: 1;
        }

        .item-details h4 {
          margin: 0 0 0.25rem 0;
          font-size: 0.9rem;
          color: var(--dark-gray);
        }

        .item-category {
          margin: 0 0 0.25rem 0;
          font-size: 0.8rem;
          color: var(--medium-gray);
        }

        .item-price {
          margin: 0;
          font-weight: 600;
          color: var(--primary-blue);
          font-size: 0.9rem;
        }

        .item-controls {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          align-items: center;
        }

        .quantity-controls {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: var(--light-gray);
          border-radius: 6px;
          padding: 0.25rem;
        }

        .qty-btn {
          background: var(--white);
          border: 1px solid var(--border-color);
          border-radius: 4px;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: var(--transition);
        }

        .qty-btn:hover {
          background: var(--primary-blue);
          color: var(--white);
        }

        .quantity {
          min-width: 30px;
          text-align: center;
          font-weight: 600;
          font-size: 0.9rem;
        }

        .remove-btn {
          background: none;
          border: none;
          color: #dc3545;
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 4px;
          transition: var(--transition);
        }

        .remove-btn:hover {
          background: rgba(220, 53, 69, 0.1);
        }

        .cart-footer {
          padding: 1.5rem;
          border-top: 1px solid var(--border-color);
          background: var(--light-gray);
        }

        .cart-total {
          margin-bottom: 1rem;
          text-align: center;
          font-size: 1.1rem;
          color: var(--primary-blue);
        }

        .cart-actions {
          display: flex;
          gap: 0.75rem;
        }

        .clear-btn, .checkout-btn {
          flex: 1;
          padding: 0.75rem;
          border: none;
          border-radius: 6px;
          font-weight: 600;
          cursor: pointer;
          transition: var(--transition);
        }

        .clear-btn {
          background: var(--border-color);
          color: var(--dark-gray);
        }

        .clear-btn:hover {
          background: var(--medium-gray);
          color: var(--white);
        }

        .checkout-btn {
          background: var(--primary-blue);
          color: var(--white);
        }

        .checkout-btn:hover {
          background: var(--secondary-gold);
          color: var(--primary-blue);
        }

        @media (max-width: 480px) {
          .cart-sidebar {
            width: 100vw;
          }
        }
      `}</style>
    </div>
  );
};

export default Cart;
