import React, { useState } from 'react';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { useNotification } from '../contexts/NotificationContext';

const Footer = () => {
  const { t } = useLanguage();
  const { showSuccess, showError } = useNotification();
  const [email, setEmail] = useState('');

  const handleNewsletterSubmit = (e) => {
    e.preventDefault();
    if (!email) {
      showError('Veuillez entrer votre adresse email');
      return;
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      showError('Veuillez entrer une adresse email valide');
      return;
    }

    // Simulate newsletter subscription
    showSuccess('Merci pour votre inscription à notre newsletter !');
    setEmail('');
  };

  const handleSocialClick = (platform) => {
    // In a real app, these would be actual social media URLs
    const urls = {
      facebook: 'https://facebook.com/poste.tunisienne',
      twitter: 'https://twitter.com/poste_tunisienne',
      instagram: 'https://instagram.com/poste.tunisienne',
      youtube: 'https://youtube.com/c/postetunisienne'
    };

    if (urls[platform]) {
      window.open(urls[platform], '_blank');
    }
  };

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          {/* Company Info */}
          <div className="footer-section">
            <div className="footer-logo">
              <div className="logo-icon">🇹🇳</div>
              <div className="logo-text">
                <h3>La Poste Tunisienne</h3>
                <p>E-Stamps</p>
              </div>
            </div>
            <p className="footer-description">
              Votre boutique officielle pour les timbres et produits philatéliques tunisiens. 
              Découvrez notre patrimoine à travers nos collections exclusives.
            </p>
            <div className="social-links">
              <button
                className="social-link"
                aria-label="Facebook"
                onClick={() => handleSocialClick('facebook')}
              >
                <Facebook size={20} />
              </button>
              <button
                className="social-link"
                aria-label="Twitter"
                onClick={() => handleSocialClick('twitter')}
              >
                <Twitter size={20} />
              </button>
              <button
                className="social-link"
                aria-label="Instagram"
                onClick={() => handleSocialClick('instagram')}
              >
                <Instagram size={20} />
              </button>
              <button
                className="social-link"
                aria-label="YouTube"
                onClick={() => handleSocialClick('youtube')}
              >
                <Youtube size={20} />
              </button>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h4 className="footer-title">Liens Rapides</h4>
            <ul className="footer-links">
              <li><Link to="/">{t('nav.home')}</Link></li>
              <li><Link to="/timbres">{t('nav.stamps')}</Link></li>
              <li><Link to="/categories">{t('nav.categories')}</Link></li>
              <li><Link to="/new-releases">{t('nav.newReleases')}</Link></li>
              <li><Link to="/about">{t('nav.about')}</Link></li>
              <li><Link to="/contact">{t('nav.contact')}</Link></li>
            </ul>
          </div>

          {/* Categories */}
          <div className="footer-section">
            <h4 className="footer-title">Collections</h4>
            <ul className="footer-links">
              <li><a href="/category/heritage">Patrimoine Tunisien</a></li>
              <li><a href="/category/nature">Nature & Paysages</a></li>
              <li><a href="/category/art">Art Contemporain</a></li>
              <li><a href="/category/personalities">Personnalités</a></li>
              <li><a href="/category/events">Événements Spéciaux</a></li>
              <li><a href="/category/sports">Sports</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h4 className="footer-title">Contact</h4>
            <div className="contact-info">
              <div className="contact-item">
                <MapPin size={18} />
                <div>
                  <p>Rue de la Poste, Tunis</p>
                  <p>1000 Tunis, Tunisie</p>
                </div>
              </div>
              <div className="contact-item">
                <Phone size={18} />
                <div>
                  <p>+216 71 123 456</p>
                  <p>Service Client</p>
                </div>
              </div>
              <div className="contact-item">
                <Mail size={18} />
                <div>
                  <p><EMAIL></p>
                  <p>Commandes en ligne</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter */}
        <div className="newsletter-section">
          <div className="newsletter-content">
            <h3>Restez informé de nos nouveautés</h3>
            <p>Inscrivez-vous à notre newsletter pour recevoir les dernières informations sur nos nouvelles émissions.</p>
          </div>
          <form className="newsletter-form" onSubmit={handleNewsletterSubmit}>
            <input
              type="email"
              placeholder="Votre adresse email"
              className="newsletter-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <button type="submit" className="newsletter-btn">S'abonner</button>
          </form>
        </div>

        {/* Bottom Bar */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; 2024 La Poste Tunisienne. Tous droits réservés.</p>
            <div className="footer-bottom-links">
              <Link to="/privacy">Politique de confidentialité</Link>
              <Link to="/terms">Conditions d'utilisation</Link>
              <Link to="/shipping">Livraison</Link>
              <Link to="/returns">Retours</Link>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .footer {
          background: var(--primary-blue);
          color: var(--white);
          padding: 3rem 0 0;
        }

        .footer-content {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr 1.5fr;
          gap: 3rem;
          margin-bottom: 3rem;
        }

        .footer-section h4 {
          color: var(--white);
        }

        .footer-logo {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 1.5rem;
        }

        .logo-icon {
          width: 50px;
          height: 50px;
          background: var(--secondary-gold);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
        }

        .logo-text h3 {
          color: var(--white);
          margin: 0;
          font-size: 1.3rem;
        }

        .logo-text p {
          color: var(--secondary-gold);
          margin: 0;
          font-weight: 600;
        }

        .footer-description {
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.6;
          margin-bottom: 2rem;
        }

        .social-links {
          display: flex;
          gap: 1rem;
        }

        .social-link {
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          border: none;
          cursor: pointer;
          transition: var(--transition);
        }

        .social-link:hover {
          background: var(--secondary-gold);
          color: var(--primary-blue);
          transform: translateY(-3px);
        }

        .footer-title {
          color: var(--secondary-gold);
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 1.5rem;
        }

        .footer-links {
          list-style: none;
          padding: 0;
        }

        .footer-links li {
          margin-bottom: 0.75rem;
        }

        .footer-links a {
          color: rgba(255, 255, 255, 0.8);
          transition: var(--transition);
        }

        .footer-links a:hover {
          color: var(--secondary-gold);
          padding-left: 0.5rem;
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .contact-item {
          display: flex;
          gap: 1rem;
          align-items: flex-start;
        }

        .contact-item svg {
          color: var(--secondary-gold);
          margin-top: 0.2rem;
          flex-shrink: 0;
        }

        .contact-item p {
          margin: 0;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.4;
        }

        .newsletter-section {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 15px;
          padding: 2rem;
          margin-bottom: 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 2rem;
        }

        .newsletter-content h3 {
          color: var(--white);
          margin-bottom: 0.5rem;
        }

        .newsletter-content p {
          color: rgba(255, 255, 255, 0.8);
          margin: 0;
        }

        .newsletter-form {
          display: flex;
          gap: 1rem;
          min-width: 350px;
        }

        .newsletter-input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: none;
          border-radius: 8px;
          font-size: 1rem;
        }

        .newsletter-input:focus {
          outline: none;
          box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
        }

        .newsletter-btn {
          background: var(--secondary-gold);
          color: var(--primary-blue);
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: var(--transition);
          white-space: nowrap;
        }

        .newsletter-btn:hover {
          background: #E6C200;
          transform: translateY(-2px);
        }

        .footer-bottom {
          border-top: 1px solid rgba(255, 255, 255, 0.2);
          padding: 2rem 0;
        }

        .footer-bottom-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: rgba(255, 255, 255, 0.8);
        }

        .footer-bottom-links {
          display: flex;
          gap: 2rem;
        }

        .footer-bottom-links a {
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.9rem;
          transition: var(--transition);
        }

        .footer-bottom-links a:hover {
          color: var(--secondary-gold);
        }

        @media (max-width: 768px) {
          .footer-content {
            grid-template-columns: 1fr;
            gap: 2rem;
          }

          .newsletter-section {
            flex-direction: column;
            text-align: center;
            gap: 1.5rem;
          }

          .newsletter-form {
            min-width: auto;
            width: 100%;
          }

          .footer-bottom-content {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
          }

          .footer-bottom-links {
            flex-wrap: wrap;
            justify-content: center;
            gap: 1rem;
          }
        }

        @media (max-width: 480px) {
          .footer {
            padding: 2rem 0 0;
          }

          .newsletter-form {
            flex-direction: column;
          }

          .social-links {
            justify-content: center;
          }
        }
      `}</style>
    </footer>
  );
};

export default Footer;
