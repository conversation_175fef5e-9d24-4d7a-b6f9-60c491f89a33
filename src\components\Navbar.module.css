.navbar {
  background: var(--white);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbarContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.navbarLogo {
  text-decoration: none;
  color: inherit;
}

.logoContainer {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logoIcon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-gold));
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.logoTextContainer {
  display: flex;
  flex-direction: column;
}

.logoTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-blue);
  line-height: 1.2;
}

.logoSubtitle {
  font-size: 0.9rem;
  color: var(--secondary-gold);
  font-weight: 500;
}

.desktopMenu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navLink {
  color: var(--dark-gray);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.navLink:hover {
  color: var(--primary-blue);
}

.navLink::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--secondary-gold);
  transition: var(--transition);
}

.navLink:hover::after {
  width: 100%;
}

.navbarActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cartBtn {
  position: relative;
  background: var(--primary-blue);
  color: var(--white);
  border: none;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
}

.cartBtn:hover {
  background: #004080;
}

.cartCount {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--secondary-gold);
  color: var(--primary-blue);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.2rem 0.4rem;
  border-radius: 50%;
  min-width: 20px;
  text-align: center;
}

.mobileMenuBtn {
  display: none;
  background: none;
  border: none;
  color: var(--primary-blue);
  cursor: pointer;
  padding: 0.5rem;
}

.mobileMenu {
  display: none;
  flex-direction: column;
  background: var(--white);
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
}

.mobileMenuOpen {
  display: flex;
}

.mobileNavLink {
  padding: 0.75rem 0;
  color: var(--dark-gray);
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.mobileNavLink:hover {
  color: var(--primary-blue);
  background: var(--light-gray);
}

@media (max-width: 768px) {
  .desktopMenu {
    display: none;
  }

  .mobileMenuBtn {
    display: block;
  }

  .logoTitle {
    font-size: 1rem;
  }

  .logoSubtitle {
    font-size: 0.8rem;
  }

  .navbarActions {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .logoTextContainer {
    display: none;
  }
}
