import React from 'react';
import ProductGrid from '../components/ProductGrid';

const Products = () => {
  // Extended products data
  const allProducts = [
    {
      id: 1,
      title: "Patrimoine Architectural Tunisien",
      description: "Collection mettant en valeur l'architecture traditionnelle tunisienne",
      price: "15.50",
      originalPrice: "18.00",
      category: "Patrimoine Tunisien",
      isNew: true,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 2,
      title: "Faune Marine de Tunisie",
      description: "Découvrez la richesse marine des côtes tunisiennes",
      price: "12.00",
      category: "Nature & Paysages",
      isNew: false,
      isLimited: true,
      rating: 4,
      inStock: true,
      image: null
    },
    {
      id: 3,
      title: "Artistes Tunisiens Contemporains",
      description: "Hommage aux grands artistes tunisiens modernes",
      price: "20.00",
      category: "Art Contemporain",
      isNew: true,
      isLimited: true,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 4,
      title: "Habib Bourguiba - Père de l'Indépendance",
      description: "Série commémorative dédiée au premier président tunisien",
      price: "25.00",
      category: "Personnalités",
      isNew: false,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 5,
      title: "Jeux Olympiques Paris 2024",
      description: "Participation tunisienne aux Jeux Olympiques",
      price: "18.50",
      category: "Sports",
      isNew: true,
      isLimited: false,
      rating: 4,
      inStock: false,
      image: null
    },
    {
      id: 6,
      title: "Révolution du Jasmin",
      description: "Commémoration de la révolution tunisienne de 2011",
      price: "22.00",
      category: "Événements Spéciaux",
      isNew: false,
      isLimited: true,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 7,
      title: "Mosquée Zitouna",
      description: "L'une des plus anciennes mosquées de Tunis",
      price: "14.00",
      category: "Patrimoine Tunisien",
      isNew: false,
      isLimited: false,
      rating: 4,
      inStock: true,
      image: null
    },
    {
      id: 8,
      title: "Désert du Sahara",
      description: "Paysages époustouflants du Sahara tunisien",
      price: "16.50",
      category: "Nature & Paysages",
      isNew: true,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 9,
      title: "Calligraphie Arabe",
      description: "Art de la calligraphie traditionnelle tunisienne",
      price: "19.00",
      category: "Art Contemporain",
      isNew: false,
      isLimited: true,
      rating: 4,
      inStock: true,
      image: null
    },
    {
      id: 10,
      title: "Tahar Haddad - Réformateur",
      description: "Hommage au grand réformateur tunisien",
      price: "17.50",
      category: "Personnalités",
      isNew: false,
      isLimited: false,
      rating: 4,
      inStock: true,
      image: null
    },
    {
      id: 11,
      title: "Coupe du Monde Qatar 2022",
      description: "Participation de l'équipe nationale tunisienne",
      price: "21.00",
      category: "Sports",
      isNew: false,
      isLimited: true,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 12,
      title: "Fête de l'Indépendance",
      description: "Célébration du 20 mars 1956",
      price: "23.50",
      category: "Événements Spéciaux",
      isNew: true,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 13,
      title: "Amphithéâtre d'El Jem",
      description: "Merveille architecturale romaine",
      price: "18.00",
      category: "Patrimoine Tunisien",
      isNew: false,
      isLimited: false,
      rating: 5,
      inStock: true,
      image: null
    },
    {
      id: 14,
      title: "Oiseaux Migrateurs",
      description: "Faune aviaire des zones humides tunisiennes",
      price: "13.50",
      category: "Nature & Paysages",
      isNew: true,
      isLimited: false,
      rating: 4,
      inStock: true,
      image: null
    },
    {
      id: 15,
      title: "Poterie de Nabeul",
      description: "Artisanat traditionnel tunisien",
      price: "15.00",
      category: "Art Contemporain",
      isNew: false,
      isLimited: false,
      rating: 4,
      inStock: true,
      image: null
    }
  ];

  return (
    <div className="products-page">
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">Catalogue de Timbres</h1>
          <p className="page-description">
            Explorez notre collection complète de timbres tunisiens. 
            Utilisez les filtres pour trouver exactement ce que vous cherchez.
          </p>
        </div>
      </div>

      <ProductGrid 
        products={allProducts} 
        showFilters={true}
      />

      <style>{`
        .products-page {
          flex: 1;
        }

        .page-header {
          background: linear-gradient(135deg, var(--primary-blue), #004080);
          color: var(--white);
          padding: 4rem 0;
          text-align: center;
        }

        .page-title {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: var(--white);
        }

        .page-description {
          font-size: 1.2rem;
          color: rgba(255, 255, 255, 0.9);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.6;
        }

        @media (max-width: 768px) {
          .page-header {
            padding: 3rem 0;
          }

          .page-title {
            font-size: 2.5rem;
          }

          .page-description {
            font-size: 1rem;
          }
        }

        @media (max-width: 480px) {
          .page-title {
            font-size: 2rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Products;
