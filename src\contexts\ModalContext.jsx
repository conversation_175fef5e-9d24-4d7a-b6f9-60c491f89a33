import React, { createContext, useContext, useState } from 'react';

const ModalContext = createContext();

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState(null);
  const [modalType, setModalType] = useState('');

  const openModal = (type, content) => {
    setModalType(type);
    setModalContent(content);
    setIsModalOpen(true);
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setModalContent(null);
    setModalType('');
    // Restore body scroll
    document.body.style.overflow = 'unset';
  };

  const openQuickView = (stamp) => {
    openModal('quickView', stamp);
  };

  const openImageGallery = (images, currentIndex = 0) => {
    openModal('imageGallery', { images, currentIndex });
  };

  const openConfirmation = (message, onConfirm, onCancel) => {
    openModal('confirmation', { message, onConfirm, onCancel });
  };

  const value = {
    isModalOpen,
    modalContent,
    modalType,
    openModal,
    closeModal,
    openQuickView,
    openImageGallery,
    openConfirmation
  };

  return (
    <ModalContext.Provider value={value}>
      {children}
    </ModalContext.Provider>
  );
};
