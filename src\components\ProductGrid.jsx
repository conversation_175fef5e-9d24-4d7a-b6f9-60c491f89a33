import React, { useState } from 'react';
import ProductCard from './ProductCard';
import { Filter, Grid, List, Search } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const ProductGrid = ({ products, title, showFilters = true }) => {
  const { t } = useLanguage();
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('newest');
  const [filterCategory, setFilterCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Get unique categories
  const categories = ['all', ...new Set(products.map(product => product.category))];

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      const matchesCategory = filterCategory === 'all' || product.category === filterCategory;
      const matchesSearch = product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesCategory && matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return parseFloat(a.price) - parseFloat(b.price);
        case 'price-high':
          return parseFloat(b.price) - parseFloat(a.price);
        case 'name':
          return a.title.localeCompare(b.title);
        case 'newest':
        default:
          return b.id - a.id;
      }
    });

  return (
    <section className="product-grid-section">
      <div className="container">
        {title && <h2 className="section-title">{title}</h2>}
        
        {showFilters && (
          <div className="filters-bar">
            <div className="search-box">
              <Search size={20} className="search-icon" />
              <input
                type="text"
                placeholder={t('products.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>

            <div className="filter-controls">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="filter-select"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'Toutes les catégories' : category}
                  </option>
                ))}
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="newest">Plus récents</option>
                <option value="name">Nom A-Z</option>
                <option value="price-low">Prix croissant</option>
                <option value="price-high">Prix décroissant</option>
              </select>

              <div className="view-toggle">
                <button
                  className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                  onClick={() => setViewMode('grid')}
                  title="Vue grille"
                >
                  <Grid size={18} />
                </button>
                <button
                  className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                  onClick={() => setViewMode('list')}
                  title="Vue liste"
                >
                  <List size={18} />
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="products-count">
          {filteredProducts.length} produit{filteredProducts.length !== 1 ? 's' : ''} trouvé{filteredProducts.length !== 1 ? 's' : ''}
        </div>

        <div className={`products-grid ${viewMode === 'list' ? 'list-view' : ''}`}>
          {filteredProducts.map(product => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="no-products">
            <div className="no-products-icon">🔍</div>
            <h3>Aucun produit trouvé</h3>
            <p>Essayez de modifier vos critères de recherche ou de filtrage.</p>
          </div>
        )}
      </div>

      <style>{`
        .product-grid-section {
          padding: 3rem 0;
        }

        .section-title {
          text-align: center;
          margin-bottom: 3rem;
          font-size: 2.5rem;
          color: var(--primary-blue);
        }

        .filters-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 2rem;
          margin-bottom: 2rem;
          padding: 1.5rem;
          background: var(--light-gray);
          border-radius: 15px;
        }

        .search-box {
          position: relative;
          flex: 1;
          max-width: 400px;
        }

        .search-icon {
          position: absolute;
          left: 1rem;
          top: 50%;
          transform: translateY(-50%);
          color: var(--medium-gray);
        }

        .search-input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 3rem;
          border: 1px solid var(--border-color);
          border-radius: 8px;
          font-size: 1rem;
          transition: var(--transition);
        }

        .search-input:focus {
          outline: none;
          border-color: var(--primary-blue);
          box-shadow: 0 0 0 3px rgba(0, 51, 102, 0.1);
        }

        .filter-controls {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .filter-select {
          padding: 0.75rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 8px;
          background: var(--white);
          font-size: 0.9rem;
          cursor: pointer;
          transition: var(--transition);
        }

        .filter-select:focus {
          outline: none;
          border-color: var(--primary-blue);
        }

        .view-toggle {
          display: flex;
          border: 1px solid var(--border-color);
          border-radius: 8px;
          overflow: hidden;
        }

        .view-btn {
          padding: 0.75rem;
          border: none;
          background: var(--white);
          cursor: pointer;
          transition: var(--transition);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .view-btn:hover {
          background: var(--light-gray);
        }

        .view-btn.active {
          background: var(--primary-blue);
          color: var(--white);
        }

        .products-count {
          margin-bottom: 1.5rem;
          color: var(--medium-gray);
          font-weight: 500;
        }

        .products-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 2rem;
        }

        .products-grid.list-view {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .no-products {
          text-align: center;
          padding: 4rem 2rem;
          color: var(--medium-gray);
        }

        .no-products-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
        }

        .no-products h3 {
          color: var(--primary-blue);
          margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
          .filters-bar {
            flex-direction: column;
            gap: 1rem;
          }

          .search-box {
            max-width: none;
            width: 100%;
          }

          .filter-controls {
            width: 100%;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.5rem;
          }

          .filter-select {
            flex: 1;
            min-width: 120px;
          }

          .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
          }

          .section-title {
            font-size: 2rem;
          }
        }

        @media (max-width: 480px) {
          .product-grid-section {
            padding: 2rem 0;
          }

          .filters-bar {
            padding: 1rem;
          }

          .filter-controls {
            flex-direction: column;
            width: 100%;
          }

          .filter-select {
            width: 100%;
          }

          .view-toggle {
            align-self: center;
          }

          .products-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
        }
      `}</style>
    </section>
  );
};

export default ProductGrid;
