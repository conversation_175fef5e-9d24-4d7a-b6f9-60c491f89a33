import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, Globe, ShoppingCart } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useCart } from '../contexts/CartContext';
import LanguageSwitcher from './LanguageSwitcher';
import styles from './Navbar.module.css';

const Navbar = () => {
  const { t } = useLanguage();
  const { getCartItemsCount, toggleCart } = useCart();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleCartClick = () => {
    toggleCart();
  };

  return (
    <nav className={styles.navbar}>
      <div className="container">
        <div className={styles.navbarContent}>
          {/* Logo */}
          <Link to="/" className={styles.navbarLogo}>
            <div className={styles.logoContainer}>
              <div className={styles.logoIcon}>
                <span>🇹🇳</span>
              </div>
              <div className={styles.logoTextContainer}>
                <span className={styles.logoTitle}>La Poste Tunisienne</span>
                <span className={styles.logoSubtitle}>E-Stamps</span>
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className={styles.desktopMenu}>
            <Link to="/" className={styles.navLink}>{t('nav.home')}</Link>
            <Link to="/products" className={styles.navLink}>{t('nav.stamps')}</Link>
            <Link to="/categories" className={styles.navLink}>{t('nav.categories')}</Link>
            <Link to="/new-releases" className={styles.navLink}>{t('nav.newReleases')}</Link>
            <Link to="/about" className={styles.navLink}>{t('nav.about')}</Link>
            <Link to="/contact" className={styles.navLink}>{t('nav.contact')}</Link>
          </div>

          {/* Right side items */}
          <div className={styles.navbarActions}>
            <LanguageSwitcher />
            <button className={styles.cartBtn} onClick={handleCartClick} title={t('nav.cart')}>
              <ShoppingCart size={20} />
              <span className={styles.cartCount}>{getCartItemsCount()}</span>
            </button>

            {/* Mobile menu button */}
            <button
              className={styles.mobileMenuBtn}
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`${styles.mobileMenu} ${isMenuOpen ? styles.mobileMenuOpen : ''}`}>
          <Link to="/" className={styles.mobileNavLink} onClick={toggleMenu}>{t('nav.home')}</Link>
          <Link to="/products" className={styles.mobileNavLink} onClick={toggleMenu}>{t('nav.stamps')}</Link>
          <Link to="/categories" className={styles.mobileNavLink} onClick={toggleMenu}>{t('nav.categories')}</Link>
          <Link to="/new-releases" className={styles.mobileNavLink} onClick={toggleMenu}>{t('nav.newReleases')}</Link>
          <Link to="/about" className={styles.mobileNavLink} onClick={toggleMenu}>{t('nav.about')}</Link>
          <Link to="/contact" className={styles.mobileNavLink} onClick={toggleMenu}>{t('nav.contact')}</Link>
        </div>
      </div>


    </nav>
  );
};

export default Navbar;
