import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, Globe, ShoppingCart } from 'lucide-react';
import LanguageSwitcher from './LanguageSwitcher';
import styles from './Navbar.module.css';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={styles.navbar}>
      <div className="container">
        <div className={styles.navbarContent}>
          {/* Logo */}
          <Link to="/" className={styles.navbarLogo}>
            <div className={styles.logoContainer}>
              <div className={styles.logoIcon}>
                <span>🇹🇳</span>
              </div>
              <div className={styles.logoTextContainer}>
                <span className={styles.logoTitle}>La Poste Tunisienne</span>
                <span className={styles.logoSubtitle}>E-Stamps</span>
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className={styles.desktopMenu}>
            <Link to="/" className={styles.navLink}>Accueil</Link>
            <Link to="/products" className={styles.navLink}>Timbres</Link>
            <Link to="/categories" className={styles.navLink}>Catégories</Link>
            <Link to="/new-releases" className={styles.navLink}>Nouveautés</Link>
            <Link to="/about" className={styles.navLink}>À propos</Link>
            <Link to="/contact" className={styles.navLink}>Contact</Link>
          </div>

          {/* Right side items */}
          <div className={styles.navbarActions}>
            <LanguageSwitcher />
            <button className={styles.cartBtn}>
              <ShoppingCart size={20} />
              <span className={styles.cartCount}>0</span>
            </button>

            {/* Mobile menu button */}
            <button
              className={styles.mobileMenuBtn}
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`${styles.mobileMenu} ${isMenuOpen ? styles.mobileMenuOpen : ''}`}>
          <Link to="/" className={styles.mobileNavLink} onClick={toggleMenu}>Accueil</Link>
          <Link to="/products" className={styles.mobileNavLink} onClick={toggleMenu}>Timbres</Link>
          <Link to="/categories" className={styles.mobileNavLink} onClick={toggleMenu}>Catégories</Link>
          <Link to="/new-releases" className={styles.mobileNavLink} onClick={toggleMenu}>Nouveautés</Link>
          <Link to="/about" className={styles.mobileNavLink} onClick={toggleMenu}>À propos</Link>
          <Link to="/contact" className={styles.mobileNavLink} onClick={toggleMenu}>Contact</Link>
        </div>
      </div>


    </nav>
  );
};

export default Navbar;
