import React from 'react';
import { ShoppingCart, Eye, Heart } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
import { useFavorites } from '../contexts/FavoritesContext';
import { useModal } from '../contexts/ModalContext';
import { useNotification } from '../contexts/NotificationContext';
import { useLanguage } from '../contexts/LanguageContext';

const ProductCard = ({ product }) => {
  const { t } = useLanguage();
  const { addToCart } = useCart();
  const { toggleFavorite, isFavorite } = useFavorites();
  const { openQuickView } = useModal();
  const { showSuccess } = useNotification();

  const {
    id,
    title,
    description,
    price,
    originalPrice,
    image,
    category,
    isNew,
    isLimited,
    rating,
    inStock
  } = product;

  const handleAddToCart = () => {
    addToCart(product);
    showSuccess(t('notifications.addedToCart'));
  };

  const handleToggleFavorite = () => {
    toggleFavorite(product);
    const message = isFavorite(id)
      ? t('notifications.removedFromFavorites')
      : t('notifications.addedToFavorites');
    showSuccess(message);
  };

  const handleQuickView = () => {
    // Convert product to stamp format for modal
    const stampFormat = {
      id,
      attributes: {
        nom: title,
        prix: price,
        image: image ? { data: { attributes: { url: image } } } : null,
        categorie: { data: { attributes: { name: category } } },
        description: [{ children: [{ text: description }] }]
      }
    };
    openQuickView(stampFormat);
  };

  return (
    <div className="product-card">
      <div className="product-image-container">
        <div className="product-image">
          {image ? (
            <img src={image} alt={title} />
          ) : (
            <div className="placeholder-image">
              <span className="placeholder-icon">📮</span>
            </div>
          )}
        </div>
        
        <div className="product-badges">
          {isNew && <span className="badge badge-new">Nouveau</span>}
          {isLimited && <span className="badge badge-limited">Édition Limitée</span>}
          {!inStock && <span className="badge badge-out-of-stock">Épuisé</span>}
        </div>

        <div className="product-actions">
          <button className="action-btn" title={t('stamps.quickView')} onClick={handleQuickView}>
            <Eye size={16} />
          </button>
          <button
            className={`action-btn ${isFavorite(id) ? 'favorite-active' : ''}`}
            title={isFavorite(id) ? t('stamps.removeFromFavorites') : t('stamps.addToFavorites')}
            onClick={handleToggleFavorite}
          >
            <Heart size={16} fill={isFavorite(id) ? 'currentColor' : 'none'} />
          </button>
        </div>
      </div>

      <div className="product-info">
        <div className="product-category">{category}</div>
        <h3 className="product-title">{title}</h3>
        <p className="product-description">{description}</p>
        
        {rating && (
          <div className="product-rating">
            {[...Array(5)].map((_, i) => (
              <span key={i} className={`star ${i < rating ? 'filled' : ''}`}>
                ⭐
              </span>
            ))}
            <span className="rating-text">({rating}/5)</span>
          </div>
        )}

        <div className="product-price">
          <span className="current-price">{price} DT</span>
          {originalPrice && originalPrice !== price && (
            <span className="original-price">{originalPrice} DT</span>
          )}
        </div>

        <button
          className={`add-to-cart-btn ${!inStock ? 'disabled' : ''}`}
          disabled={!inStock}
          onClick={handleAddToCart}
        >
          <ShoppingCart size={16} />
          {inStock ? t('stamps.addToCart') : t('products.outOfStock')}
        </button>
      </div>

      <style>{`
        .product-card {
          background: var(--white);
          border-radius: 15px;
          overflow: hidden;
          box-shadow: var(--shadow);
          transition: var(--transition);
          position: relative;
        }

        .product-card:hover {
          transform: translateY(-8px);
          box-shadow: var(--shadow-hover);
        }

        .product-image-container {
          position: relative;
          overflow: hidden;
        }

        .product-image {
          width: 100%;
          height: 250px;
          overflow: hidden;
        }

        .product-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: var(--transition);
        }

        .product-card:hover .product-image img {
          transform: scale(1.1);
        }

        .placeholder-image {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, var(--light-gray), var(--border-color));
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px dashed var(--primary-blue);
        }

        .placeholder-icon {
          font-size: 4rem;
          opacity: 0.5;
        }

        .product-badges {
          position: absolute;
          top: 1rem;
          left: 1rem;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .badge {
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
        }

        .badge-new {
          background: var(--secondary-gold);
          color: var(--primary-blue);
        }

        .badge-limited {
          background: #FF6B6B;
          color: var(--white);
        }

        .badge-out-of-stock {
          background: var(--medium-gray);
          color: var(--white);
        }

        .product-actions {
          position: absolute;
          top: 1rem;
          right: 1rem;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          opacity: 0;
          transition: var(--transition);
        }

        .product-card:hover .product-actions {
          opacity: 1;
        }

        .action-btn {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: none;
          background: var(--white);
          color: var(--primary-blue);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: var(--transition);
          box-shadow: var(--shadow);
        }

        .action-btn:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: scale(1.1);
        }

        .action-btn.favorite-active {
          background: var(--primary-blue);
          color: var(--white);
        }

        .action-btn.favorite-active:hover {
          background: #dc3545;
        }

        .product-info {
          padding: 1.5rem;
        }

        .product-category {
          color: var(--secondary-gold);
          font-size: 0.85rem;
          font-weight: 600;
          text-transform: uppercase;
          margin-bottom: 0.5rem;
        }

        .product-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--primary-blue);
          margin-bottom: 0.75rem;
          line-height: 1.3;
        }

        .product-description {
          color: var(--medium-gray);
          font-size: 0.9rem;
          line-height: 1.5;
          margin-bottom: 1rem;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-rating {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          margin-bottom: 1rem;
        }

        .star {
          font-size: 0.9rem;
          opacity: 0.3;
        }

        .star.filled {
          opacity: 1;
        }

        .rating-text {
          font-size: 0.8rem;
          color: var(--medium-gray);
          margin-left: 0.5rem;
        }

        .product-price {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 1.5rem;
        }

        .current-price {
          font-size: 1.4rem;
          font-weight: 700;
          color: var(--primary-blue);
        }

        .original-price {
          font-size: 1rem;
          color: var(--medium-gray);
          text-decoration: line-through;
        }

        .add-to-cart-btn {
          width: 100%;
          padding: 0.75rem;
          background: var(--primary-blue);
          color: var(--white);
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: var(--transition);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
        }

        .add-to-cart-btn:hover:not(.disabled) {
          background: #004080;
          transform: translateY(-2px);
        }

        .add-to-cart-btn.disabled {
          background: var(--medium-gray);
          cursor: not-allowed;
          opacity: 0.6;
        }

        @media (max-width: 480px) {
          .product-info {
            padding: 1rem;
          }

          .product-title {
            font-size: 1.1rem;
          }

          .current-price {
            font-size: 1.2rem;
          }
        }
      `}</style>
    </div>
  );
};

export default ProductCard;
