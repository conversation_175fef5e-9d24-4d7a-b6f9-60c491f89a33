import React, { useEffect, useState } from "react";
import axios from "axios";

function Timbres() {
  const [timbres, setTimbres] = useState([]);

  useEffect(() => {
    axios
      .get("http://localhost:1337/api/timbres?populate=*")
      .then((res) => {
        console.log("Données reçues:", res.data.data);
        setTimbres(res.data.data);
      })
      .catch((err) => console.error("Erreur API:", err));
  }, []);

  return (
    <div style={{ padding: "20px" }}>
      <h1>Liste des Timbres</h1>
      {timbres.map((timbre) => {
        const { id, attributes } = timbre;
        const imageUrl = `http://localhost:1337${attributes.image.data.attributes.url}`;
        const description = attributes.description
          .map((block) => block.children.map((c) => c.text).join(" "))
          .join("\n");

        return (
          <div key={id} style={{ border: "1px solid #ccc", padding: "10px", marginBottom: "15px" }}>
            <h2>{attributes.nom}</h2>
            <img src={imageUrl} alt={attributes.nom} width="300" />
            <p><strong>Prix:</strong> {attributes.prix} DT</p>
            <p><strong>Description:</strong> {description}</p>
            <p><strong>Catégorie:</strong> {attributes.categorie.data.attributes.name}</p>
          </div>
        );
      })}
    </div>
  );
}

export default Timbres;
