import React, { useEffect, useState } from "react";
import axios from "axios";
import StampCard from "../components/StampCard";

function Timbres() {
  const [timbres, setTimbres] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTimbres = async () => {
      try {
        setLoading(true);
        const response = await axios.get("http://localhost:1337/api/timbres?populate=*");
        console.log("Données reçues:", response.data.data);
        setTimbres(response.data.data);
        setError(null);
      } catch (err) {
        console.error("Erreur API:", err);
        setError("Erreur lors du chargement des timbres");
      } finally {
        setLoading(false);
      }
    };

    fetchTimbres();
  }, []);

  return (
    <div className="timbres-page">
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">Collection de Timbres</h1>
          <p className="page-description">
            Découvrez notre collection complète de timbres tunisiens disponibles.
          </p>
        </div>
      </div>

      <div className="container">
        {loading && (
          <div className="loading-message">
            <p>Chargement des timbres...</p>
          </div>
        )}

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        {!loading && !error && timbres.length > 0 && (
          <div className="timbres-grid">
            {timbres.map((timbre) => (
              <StampCard key={timbre.id} stamp={timbre} />
            ))}
          </div>
        )}

        {!loading && !error && timbres.length === 0 && (
          <div className="no-timbres-message">
            <p>Aucun timbre disponible pour le moment.</p>
          </div>
        )}
      </div>

      <style>{`
        .timbres-page {
          min-height: 100vh;
          background: var(--light-gray);
        }

        .page-header {
          background: var(--primary-blue);
          color: var(--white);
          padding: 4rem 0 2rem;
          text-align: center;
        }

        .page-title {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: var(--white);
        }

        .page-description {
          font-size: 1.2rem;
          color: rgba(255, 255, 255, 0.9);
          max-width: 600px;
          margin: 0 auto;
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
        }

        .timbres-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          padding: 3rem 0;
        }

        .loading-message,
        .error-message,
        .no-timbres-message {
          text-align: center;
          padding: 4rem 2rem;
          font-size: 1.1rem;
        }

        .loading-message p {
          color: var(--medium-gray);
        }

        .error-message p {
          color: #dc3545;
          font-weight: 600;
        }

        .no-timbres-message p {
          color: var(--medium-gray);
        }

        @media (max-width: 768px) {
          .page-header {
            padding: 3rem 0 1.5rem;
          }

          .page-title {
            font-size: 2.5rem;
          }

          .page-description {
            font-size: 1.1rem;
          }

          .timbres-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            padding: 2rem 0;
          }
        }

        @media (max-width: 480px) {
          .page-header {
            padding: 2rem 0 1rem;
          }

          .page-title {
            font-size: 2rem;
          }

          .timbres-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
        }
      `}</style>
    </div>
  );
}

export default Timbres;
