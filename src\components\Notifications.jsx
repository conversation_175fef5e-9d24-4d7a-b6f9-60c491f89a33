import React from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const Notifications = () => {
  const { notifications, removeNotification } = useNotification();

  if (notifications.length === 0) return null;

  const getIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircle size={20} />;
      case 'error':
        return <AlertCircle size={20} />;
      case 'warning':
        return <AlertTriangle size={20} />;
      case 'info':
      default:
        return <Info size={20} />;
    }
  };

  const getTypeClass = (type) => {
    switch (type) {
      case 'success':
        return 'notification-success';
      case 'error':
        return 'notification-error';
      case 'warning':
        return 'notification-warning';
      case 'info':
      default:
        return 'notification-info';
    }
  };

  return (
    <div className="notifications-container">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`notification ${getTypeClass(notification.type)}`}
        >
          <div className="notification-icon">
            {getIcon(notification.type)}
          </div>
          
          <div className="notification-message">
            {notification.message}
          </div>
          
          <button
            className="notification-close"
            onClick={() => removeNotification(notification.id)}
          >
            <X size={16} />
          </button>
        </div>
      ))}

      <style>{`
        .notifications-container {
          position: fixed;
          top: 2rem;
          right: 2rem;
          z-index: 4000;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          max-width: 400px;
        }

        .notification {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          background: var(--white);
          border-left: 4px solid;
          animation: slideIn 0.3s ease-out;
          min-height: 60px;
        }

        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        .notification-success {
          border-left-color: #28a745;
          background: #f8fff9;
        }

        .notification-success .notification-icon {
          color: #28a745;
        }

        .notification-error {
          border-left-color: #dc3545;
          background: #fff8f8;
        }

        .notification-error .notification-icon {
          color: #dc3545;
        }

        .notification-warning {
          border-left-color: #ffc107;
          background: #fffef8;
        }

        .notification-warning .notification-icon {
          color: #ffc107;
        }

        .notification-info {
          border-left-color: var(--primary-blue);
          background: #f8f9ff;
        }

        .notification-info .notification-icon {
          color: var(--primary-blue);
        }

        .notification-icon {
          flex-shrink: 0;
        }

        .notification-message {
          flex: 1;
          font-size: 0.9rem;
          line-height: 1.4;
          color: var(--dark-gray);
        }

        .notification-close {
          background: none;
          border: none;
          color: var(--medium-gray);
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 4px;
          transition: var(--transition);
          flex-shrink: 0;
        }

        .notification-close:hover {
          background: rgba(0, 0, 0, 0.1);
          color: var(--dark-gray);
        }

        @media (max-width: 768px) {
          .notifications-container {
            top: 1rem;
            right: 1rem;
            left: 1rem;
            max-width: none;
          }

          .notification {
            padding: 0.75rem;
          }

          .notification-message {
            font-size: 0.85rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Notifications;
