/* Tunisian Post E-Stamps Website - Global Styles */
:root {
  /* Tunisian Post Official Colors */
  --primary-blue: #003366;
  --secondary-gold: #FFD700;
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --medium-gray: #6C757D;
  --dark-gray: #343A40;
  --border-color: #DEE2E6;
  --shadow: 0 2px 10px rgba(0, 51, 102, 0.1);
  --shadow-hover: 0 4px 20px rgba(0, 51, 102, 0.15);

  /* Typography */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Transitions */
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: inherit;
  color: var(--dark-gray);
  background-color: var(--white);
  line-height: 1.6;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-blue);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
  margin-bottom: 1rem;
  color: var(--medium-gray);
}

/* Links */
a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-gold);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #004080;
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.btn-secondary {
  background-color: var(--secondary-gold);
  color: var(--primary-blue);
}

.btn-secondary:hover {
  background-color: #E6C200;
  transform: translateY(-2px);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-outline:hover {
  background-color: var(--primary-blue);
  color: var(--white);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Grid System */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* Cards */
.card {
  background: var(--white);
  border-radius: 10px;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.card-body {
  padding: 1.5rem;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.py-1 { padding: 0.5rem 0; }
.py-2 { padding: 1rem 0; }
.py-3 { padding: 1.5rem 0; }
.py-4 { padding: 2rem 0; }
.py-5 { padding: 3rem 0; }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }

  .grid {
    gap: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Language Switcher Styles */
.language-switcher {
  position: relative;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--light-gray);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.language-btn:hover {
  background: var(--border-color);
}

.current-lang {
  font-weight: 500;
}

.chevron {
  transition: var(--transition);
}

.chevron-open {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: var(--shadow);
  min-width: 150px;
  z-index: 1000;
  margin-top: 0.25rem;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border: none;
  background: none;
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.language-option:hover {
  background: var(--light-gray);
}

.language-option.active {
  background: var(--primary-blue);
  color: var(--white);
}

.lang-flag {
  font-size: 1.2rem;
}

.lang-name {
  font-weight: 500;
}
